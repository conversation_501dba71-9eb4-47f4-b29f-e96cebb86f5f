import React, { useEffect, useState, createContext } from 'react';
import SearchBar from './components/search-bar';
import StatusBar from './components/status-bar';
import LoginDialog from '../../components/LoginDialog';
import { useAppModeStore } from '../../stores/appModeStore';
import { useAuthStore } from '../../stores/authStore';
import { useSystemStore } from '../../stores/systemStore';
import xiaozhi from '../../api/xiaozhi';
import deviceApi from '../../api/device';
import { changeLanguage } from '../../i18n';
import useMCPToolsStore from '../../stores/mcpToolsStore';
import { AppMode, ResultItem } from '../../types/app';
import HomeRouter from './router';
import { appManagerClient } from '../../services/api/app-manager';
import { windowManagerClient } from '../../services/api/window-manager';
import i18n from '../../i18n';
import { Command, FileSearch as FileSearchIcon, Home, Clipboard, Languages, Camera } from 'lucide-react';
import { Avatar } from 'antd';
import { getWebSocketManager } from '../../llm/services/WebSocketChatService';

// 路由类型定义
export type RouteType = '/' | '/ai-chat' | '/file-search' | '/clipboard' | '/translate';

// Context定义（从constants/app.tsx迁移）
export const SearchBarContext = createContext<{ query: string }>({ query: '' });
export const ComposingContext = createContext<{ isComposing: boolean }>({ isComposing: false });

// 通用样式常量（从constants/app.tsx迁移）
const COMMON_STYLES = {
  // 图标容器样式
  APP_ICON: 'w-8 h-8 p-0.5 mr-2 rounded-[8px]',
  DEFAULT_ICON: 'w-8 h-8 p-1 mr-2 rounded-[8px]',
} as const;

// 应用默认配置（从constants/app.tsx迁移）
export const APP_CONFIG = {
  maxResults: 10,
  debounceTime: 300,
  minQueryLength: 1
};

// 路由与AppMode的映射关系
const routeToAppModeMap: Record<RouteType, AppMode> = {
  '/': AppMode.SEARCH,
  '/ai-chat': AppMode.AI_CHAT,
  '/file-search': AppMode.FILE_SEARCH,
  '/clipboard': AppMode.CLIPBOARD_HISTORY,
  '/translate': AppMode.TRANSLATOR
};

const appModeToRouteMap: Record<AppMode, RouteType> = {
  [AppMode.HOME]: '/',
  [AppMode.SEARCH]: '/',
  [AppMode.AI_CHAT]: '/ai-chat',
  [AppMode.FILE_SEARCH]: '/file-search',
  [AppMode.CLIPBOARD_HISTORY]: '/clipboard',
  [AppMode.TRANSLATOR]: '/translate'
};

// 创建单个应用项的函数（从constants/app.tsx迁移）
export const getAppItem = (app: any): ResultItem => {
  return {
    id: app.id || `app-${app.path}`,
    name: app.name,
    shortcut: app.shortcut || '',
    path: app.path,
    renderIcon: () => app.icon 
      ? <Avatar src={app.icon} className={COMMON_STYLES.APP_ICON} />
      : <div className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async () => {
      console.log('启动应用:', app.name, app.path);
      try {
        // 使用 app-manager 接口启动应用
        await appManagerClient.launchApp(app.path);
        // 隐藏窗口
        windowManagerClient.hideWindow();
      } catch (error) {
        console.error('应用启动失败:', error);
      }
    }
  };
};

// 创建MCP技能项（从constants/app.tsx迁移）
export const getMcpToolItem = (tool: any): ResultItem => {
  const resultItem: ResultItem = {
    id: `mcp-tool-${tool.id}`,
    name: tool.c_name|| tool.name || tool.fullName ,
    shortcut: '',
    renderIcon: () => tool.logoUrl 
      ? <Avatar src={tool.logoUrl} className={COMMON_STYLES.DEFAULT_ICON}/>
      : <Command className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async (query: string) => {
      console.log('执行MCP技能:', tool.name, '参数:', query);
      try {
        // 先切换到AI对话模式
        const switchEvent = new CustomEvent('mcp-switch-to-ai-chat', {
          detail: {
            toolInfo: {
              id: tool.id,
              c_name: tool.c_name,
              description: tool.description,
              descriptionChinese: tool.descriptionChinese,
              fullName: tool.fullName,
              inputSchema: tool.inputSchema,
              name: tool.name,
              outputSchema: tool.outputSchema,
              points: tool.points,
              projectUUId: tool.projectUUId,
              regex: tool.regex,
              logoUrl: tool.logoUrl,
              is_single_call: tool.is_single_call
            },
            toolMessage: tool.c_name || tool.name
          }
        });
        window.dispatchEvent(switchEvent);

        // 重构前的逻辑：直接通过IPC调用MCP工具
        // 但是首先需要确保MCP服务器已连接
        setTimeout(async () => {
          try {
            console.log('🔧 调用MCP工具:', tool.name, '项目ID:', tool.projectUUId);

            // 首先检查MCP服务器是否已连接
            try {
              const tools = await window.electron.mcp.listTools();
              console.log('📋 当前可用的MCP工具:', tools.map(t => t.name));

              const targetTool = tools.find(t => t.name === `${tool.projectUUId}--${tool.name}`);
              if (!targetTool) {
                console.warn('⚠️ 目标工具不在可用工具列表中，尝试重新加载MCP服务器');

                // 尝试重新加载单个服务器
                const reloadResult = await window.electron.mcp.loadSingleServer(tool.projectUUId);
                console.log('🔄 重新加载服务器结果:', reloadResult);

                if (!reloadResult) {
                  throw new Error(`无法加载MCP服务器: ${tool.projectUUId}`);
                }

                // 等待一段时间让服务器完全加载
                await new Promise(resolve => setTimeout(resolve, 2000));
              }
            } catch (listError) {
              console.error('❌ 检查MCP工具列表失败:', listError);
            }

            // 构建工具调用参数
            const toolArgs = {}; // 可以根据需要添加参数

            // 调用MCP工具（使用完整的工具名称）
            const fullToolName = `${tool.projectUUId}--${tool.name}`;
            console.log('🚀 调用MCP工具:', fullToolName);

            const result = await window.electron.mcp.callTool({
              client: tool.projectUUId,
              name: fullToolName, // 使用完整的工具名称
              args: toolArgs
            });

            console.log('✅ MCP工具调用结果:', result);

            if (result.error) {
              console.error('❌ MCP工具调用失败:', result.error);
            } else {
              console.log('🎉 MCP工具调用成功:', result.result);
            }
          } catch (error) {
            console.error('❌ MCP工具调用异常:', error);
          }
        }, 300);
      } catch (error) {
        console.error('MCP技能执行失败:', error);
      }
    }
  };
  
  // 保存原始技能数据，供搜索使用
  (resultItem as any).originalTool = tool;
  (resultItem as any).c_name = tool.c_name;
  (resultItem as any).originalName = tool.name;
  (resultItem as any).fullName = tool.fullName;
  (resultItem as any).supportedExtensions = tool.supportedExtensions;
  
  return resultItem;
};

// 创建文件路径上下文
interface FilePathContextType {
  droppedFilePath: string | null;
  setDroppedFilePath: (path: string | null) => void;
}

export const FilePathContext = createContext<FilePathContextType>({
  droppedFilePath: null,
  setDroppedFilePath: () => {}
});

// 获取MCP技能列表（从constants/app.tsx迁移）
export const getMcpTools = (): ResultItem[] => {
  try {
    // 从store中获取缓存的技能数据，避免频繁的IPC调用
    const tools = useMCPToolsStore.getState().tools;
    
    // console.log(`从缓存获取到 ${tools.length} 个MCP技能`);
    
    const resultItems = tools.map((tool: any) => {
      return getMcpToolItem(tool);
    });
    return resultItems;
  } catch (error) {
    console.error('从缓存获取MCP技能失败:', error);
    return [];
  }
};

// 创建截图工具项（从constants/app.tsx迁移）
export const getScreenshotResultItem = (): ResultItem => {
  return {
    id: 'screenshot-tool',
    name: i18n.t('screenshot.title', '截图'),
    shortcut: '',
    renderIcon: () => <Camera className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async () => {
      console.log('启动截图功能');
      try {
        // 调用双击悬浮球的截图功能
        if (window.electron?.floatingBall?.doubleClick) {
          window.electron.floatingBall.doubleClick();
        }
        // 隐藏主窗口
        windowManagerClient.hideWindow();
      } catch (error) {
        console.error('截图功能启动失败:', error);
      }
    }
  };
};

// 获取所有内置工具项（从constants/app.tsx迁移并简化）
export const getBuiltinTools = (
  onAiChatStart?: (query: string) => void,
  onFileSearchStart?: (query: string) => void,
  onClipboardHistoryBack?: () => void,
  onTranslateStart?: () => void
): ResultItem[] => {
  return [
    // AI对话工具
    {
      id: 'ai-chat-tool',
      name: i18n.t('ai.chatMode', 'AI对话'),
      shortcut: '',
      renderIcon: () => <Command className={COMMON_STYLES.DEFAULT_ICON} />,
      execute: async (q: string) => {
        console.log('启动AI对话:', q);
        if (onAiChatStart) {
          onAiChatStart(q);
        }
      }
    },
    // 文件搜索工具
    {
      id: 'file-search-tool',
      name: i18n.t('fileSearch.fileSearch', '文件搜索'),
      shortcut: '',
      renderIcon: () => <FileSearchIcon className={COMMON_STYLES.DEFAULT_ICON} />,
      execute: async (q: string) => {
        console.log('启动文件搜索:', q);
        if (onFileSearchStart) {
          onFileSearchStart(q);
        }
      }
    },
    // 剪贴板历史工具
    {
      id: 'clipboard-history-tool',
      name: i18n.t('clipboard.history', '剪贴板历史'),
      shortcut: '',
      renderIcon: () => <Clipboard className={COMMON_STYLES.DEFAULT_ICON} />,
      execute: async () => {
        console.log('启动剪贴板历史');
        try {
          // 调用剪贴板历史功能
          if (window.electron?.clipboardHistory?.show) {
            await window.electron.clipboardHistory.show();
          }
          console.log('剪贴板历史已启动，保持主窗口可见');
        } catch (error) {
          console.error('显示剪贴板历史失败:', error);
        }
      }
    },
    // 翻译工具
    {
      id: 'translate-tool',
      name: i18n.t('translate.title', '翻译'),
      shortcut: '',
      renderIcon: () => <Languages className={COMMON_STYLES.DEFAULT_ICON} />,
      execute: async (query: string) => {
        console.log('启动翻译功能:', query);
        try {
          if (onTranslateStart) {
            onTranslateStart();
          }
        } catch (error) {
          console.error('翻译功能错误:', error);
        }
      }
    },
    // 截图工具
    getScreenshotResultItem()
  ];
};


// 创建AI_CHAT模式项（从constants/app.tsx迁移并简化）
export const getAiResultItem = (onAiChatStart?: (query: string) => void): ResultItem => {
  return {
    id: 'ai-chat-mode',
    name: i18n.t('ai.chatMode', 'AI对话'),
    shortcut: '',
    renderIcon: () => <Command className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async (query: string) => {
      console.log('启动AI对话模式:', query);
      if (onAiChatStart) {
        onAiChatStart(query);
      }
    }
  };
};

// 创建FILE_SEARCH模式项（从constants/app.tsx迁移并简化）
export const getFileSearchResultItem = (onFileSearchStart?: (query: string) => void): ResultItem => {
  return {
    id: 'file-search-mode',
    name: i18n.t('fileSearch.fileSearch', '文件搜索'),
    shortcut: '',
    renderIcon: () => <FileSearchIcon className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async (query: string) => {
      console.log('启动文件搜索模式:', query);
      if (onFileSearchStart) {
        onFileSearchStart(query);
      }
    }
  };
};

// 创建CLIPBOARD_HISTORY模式项（从constants/app.tsx迁移）
export const getClipboardHistoryResultItem = (onBack?: () => void): ResultItem => {
  return {
    id: 'clipboard-history-mode',
    name: i18n.t('clipboard.history', '剪贴板历史'),
    shortcut: '',
    renderIcon: () => <Clipboard className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async () => {
      console.log('启动剪贴板历史模式');
      try {
        if (window.electron?.clipboardHistory?.show) {
          await window.electron.clipboardHistory.show();
        }
        if (onBack) {
          onBack();
        }
      } catch (error) {
        console.error('剪贴板历史启动失败:', error);
      }
    }
  };
};

// 创建翻译工具项（从constants/app.tsx迁移）
export const getTranslateResultItem = (onTranslateStart?: () => void): ResultItem => {
  return {
    id: 'translate-mode',
    name: i18n.t('translate.title', '翻译'),
    shortcut: '',
    renderIcon: () => <Languages className={COMMON_STYLES.DEFAULT_ICON} />,
    execute: async (query: string) => {
      console.log('启动翻译模式:', query);
      try {
        if (onTranslateStart) {
          onTranslateStart();
        }
      } catch (error) {
        console.error('翻译功能错误:', error);
      }
    }
  };
};

// 主窗口首页组件
const HomePage: React.FC = () => {
  const [showSystemInfo, setShowSystemInfo] = useState(false);
  const [droppedFilePath, setDroppedFilePath] = useState<string | null>(null);
  
  // 路由状态管理
  const [currentRoute, setCurrentRoute] = useState<RouteType>('/');
  const [query, setQuery] = useState('');

  // 状态栏项点击处理函数
  const handleStatusBarItemClick = (action: string) => {
    console.log(`状态栏动作: ${action}`);
    if (action === 'systemInfo') {
      setShowSystemInfo(!showSystemInfo);
    }
    // 这里可以添加实际的功能处理
  };

  // 创建一个状态来触发SearchBar的重置
  const [aiChatCompleteCounter, setAiChatCompleteCounter] = useState(0);

  // AI对话完成回调处理函数
  const handleAiChatComplete = () => {
    console.log('AI对话完成，重置搜索栏状态');
    // 通过增加计数器来触发SearchBar的重置
    setAiChatCompleteCounter(prev => prev + 1);
    // SearchBar内部会处理setQuery('')，这里不需要重复处理
  };

  // 路由切换处理函数
  const handleRouteChange = (route: RouteType, searchQuery?: string) => {
    console.log(`路由切换: ${currentRoute} -> ${route}`);
    setCurrentRoute(route);

    // 同步更新AppMode状态（保持向后兼容）
    const appMode = routeToAppModeMap[route];
    setAppMode(appMode);

    // 如果提供了搜索查询，更新查询状态
    if (searchQuery !== undefined) {
      setQuery(searchQuery);
    }
  };

  // 返回首页的处理函数
  const handleBackToHome = () => {
    handleRouteChange('/');
    setQuery('');
  };

  // 工具函数：从路由获取AppMode
  const getAppModeFromRoute = (route: RouteType): AppMode => {
    return routeToAppModeMap[route];
  };

  // 工具函数：从AppMode获取路由
  const getRouteFromAppMode = (appMode: AppMode): RouteType => {
    return appModeToRouteMap[appMode];
  };

  // 获取初始化方法
  const setAppMode = useAppModeStore((state) => state.setAppMode);
  const { checkAuthStatus, openLoginDialog } = useAuthStore();
  const fetchSystemInfo = useSystemStore((state) => state.fetchSystemInfo);
  const setWebsocketUrl = useSystemStore((state) => state.setWebsocketUrl);
  const loadMCPTools = useMCPToolsStore((state) => state.loadTools);
  const refreshMCPTools = useMCPToolsStore((state) => state.refreshTools);
  
  // 监听AppMode变化，同步更新路由状态
  const currentAppMode = useAppModeStore((state) => state.currentMode);
  
  useEffect(() => {
    const newRoute = getRouteFromAppMode(currentAppMode);
    if (newRoute !== currentRoute) {
      console.log(`AppMode变化: ${currentAppMode} -> 路由: ${newRoute}`);
      setCurrentRoute(newRoute);
    }
  }, [currentAppMode, currentRoute]);
  


  // 注册ESC键监听器
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !e.ctrlKey && !e.altKey && !e.metaKey && !e.shiftKey) {
        console.log('捕获到ESC键，当前路由:', currentRoute, 'query:', query);

        // 如果有搜索查询，让search-bar先处理（清空搜索）
        if (query) {
          console.log('有搜索查询，让search-bar处理ESC键');
          return; // 不阻止事件，让search-bar处理
        }

        // 如果不在首页，则返回首页
        if (currentRoute !== '/') {
          e.preventDefault(); // 阻止默认行为
          e.stopPropagation(); // 阻止事件冒泡

          console.log('返回首页');
          handleBackToHome();
        } else {
          // 如果已经在首页且没有搜索内容，关闭窗口
          console.log('已在首页且无搜索内容，关闭窗口');
          e.preventDefault(); // 阻止默认行为
          if (window.electron?.window?.hideWindow) {
            window.electron.window.hideWindow();
          }
        }
      }
    };

    // 使用普通的事件监听，让search-bar有机会先处理
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentRoute, query, handleBackToHome]);

  // 监听用户状态变化事件
  useEffect(() => {
    const handleUserStatusChange = (authState: any) => {

      // 更新本地 authStore 状态，使用标志避免循环广播
      if (authState && authState.user) {
        console.log('📝 主窗口更新为登录状态');
        // 设置跳过广播标志，然后更新完整的认证状态
        useAuthStore.setState({
          _skipBroadcast: false, // 重置标志，准备接受下次操作
          user: authState.user,
          token: authState.token,
          isLoggedIn: true,
          loginDialogOpen: false // 关闭登录弹窗
        });
      } else {
        console.log('📝 主窗口更新为登出状态');
        // 登出状态：直接更新状态，不要调用logout方法，避免循环广播
        useAuthStore.setState({ 
          user: null,
          token: null,
          isLoggedIn: false,
          loginDialogOpen: false,
          _skipBroadcast: false // 重置标志，准备接受下次操作
        });
      }
    };

    // 监听来自主进程的用户状态变化事件
    const removeListener = window.electron.system.onUserStatusChanged(handleUserStatusChange);

    return () => {
      removeListener();
    };
  }, []);
  
  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`主窗口收到语言变化事件: ${language}`);
      changeLanguage(language);
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, []);
  
  // 监听MCP技能更新事件
  useEffect(() => {
    const handler = () => {
      console.log('🔄 收到MCP技能更新事件，刷新缓存...');
      refreshMCPTools();
    };
    // 注册监听，返回取消函数
    const removeListener = window.electron.mcp.onToolsUpdated(handler);
    return () => {
      removeListener();
    };
  }, [refreshMCPTools]);
  
  // 组件挂载时初始化业务逻辑
  useEffect(() => {
    const initializeHomePage = async () => {
      // 检查认证状态（request技能类会自动处理token获取）
      await checkAuthStatus();

      // 确保系统信息先加载完成
      try {
        // 初始化MCP技能缓存
        console.log('🚀 开始初始化MCP技能缓存...');
        await loadMCPTools();
        const info = await fetchSystemInfo();
        console.log('✅ 系统信息加载完成:', info.computerId);

        // 系统信息加载完成后再进行其他操作
        deviceApi.deviceReport({
          "deviceId": info.computerId,
          "osName": info.platform,
          "osVersion": info.version,
          "userHomeDir": JSON.stringify([info.homePath, info.desktopPath, info.documentsPath]),
          "osArch":  info.arch
        })
   
        const params: any = {
          "version": 2,
          "flash_size": 16777216,
          "psram_size": 0,
          "minimum_free_heap_size": 8318916,
          "mac_address": info.computerId,
          "uuid": info.computerId,
          "chip_model_name": "esp32s3",
          "chip_info": {"model": 9, "cores": 2, "revision": 2, "features": 18},
          "application": {
            "name": "xiaozhi",
            "version": "1.1.2",
            "idf_version": "v5.3.2-dirty",
          },
          "partition_table": [],
          "ota": {"label": "factory"},
          "board": { 
            "type": "bread-compact-wifi", 
            "ip": info.computerId,
            "mac": info.computerId,
          },
          "isAido": true 
        }
        xiaozhi.ota(params).then((res: any) => {
          // 将 WebSocket URL 存储到 systemStore
          if (res.websocket && res.websocket.url) {
            localStorage.setItem('otaSocketUrl', res.websocket.url);
            setWebsocketUrl(res.websocket.url);
          }
        }).catch((e) => {
        });
      } catch (e) {
        console.error('系统信息初始化失败:', e);
      }
    };
    initializeHomePage();
  }, [checkAuthStatus, fetchSystemInfo, loadMCPTools]);

  // 监听主进程要求打开登录弹框的事件
  useEffect(() => {
    const cleanup = window.electron.auth.onOpenLoginDialog(() => {
      console.log('🔓 收到主进程要求打开登录弹框的事件');
      openLoginDialog();
      console.log('🔓 登录弹框已打开');
    });

    // 监听自定义事件（备用方案）
    const handleCustomOpenLogin = () => {
      console.log('🔓 收到自定义事件要求打开登录弹框');
      openLoginDialog();
      console.log('🔓 登录弹框已通过自定义事件打开');
    };

    window.addEventListener('open-login-dialog', handleCustomOpenLogin);

    console.log('🔓 已注册登录弹框事件监听器');
    
    return () => {
      cleanup();
      window.removeEventListener('open-login-dialog', handleCustomOpenLogin);
    };
  }, [openLoginDialog]);

  // 监听文件拖拽事件
  useEffect(() => {
    const getDropFilePath = window.electron.crossWindow.on('pet:files-dropped', (data) => {
      // 检查data是否包含文件路径
      if (data && typeof data === 'object' && data.filesPath && Array.isArray(data.filesPath) && data.filesPath.length > 0) {
        const filePath = data.filesPath.join(','); 
        setDroppedFilePath(filePath);
      }
    });
    // 清理函数
    return () => {
      getDropFilePath();
    };
  }, [setDroppedFilePath]);

  return (
    <FilePathContext.Provider value={{ droppedFilePath, setDroppedFilePath }}>
      <SearchBarContext.Provider value={{ query }}>
        {/* 主容器，禁用滚动，使用flex布局 */}
        <div className="h-screen flex flex-col overflow-hidden app-body bg-gray-50 dark:bg-gray-800">
          {/* 上层：搜索栏 */}
          <div className="flex-shrink-0">
            <SearchBar
              query={query}
              onQueryChange={setQuery}
              onRouteChange={handleRouteChange}
              currentRoute={currentRoute}
              onBackToHome={handleBackToHome}
              onAiChatComplete={handleAiChatComplete}
              aiChatCompleteCounter={aiChatCompleteCounter}
            />
          </div>
          
          {/* 中层：路由渲染区域 */}
          <div className="flex-1 overflow-hidden">
            <HomeRouter
              currentRoute={currentRoute}
              query={query}
              onRouteChange={handleRouteChange}
              onBackToHome={handleBackToHome}
              onAiChatComplete={handleAiChatComplete}
            />
          </div>
          
          {/* 下层：状态栏 */}
          <div className="flex-shrink-0">
            <StatusBar 
              // items={[
              //   { label: '打开', onClick: () => handleStatusBarItemClick('open') },
              //   { label: '打开位置', onClick: () => handleStatusBarItemClick('openLocation') },
              //   { label: '关于', onClick: () => handleStatusBarItemClick('about') }
              // ]}
            />
          </div>
          
          {/* 登录对话框 */}
          <LoginDialog />
          <div id="captcha" style={{zIndex: 999999, pointerEvents: 'auto'}} onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
          }}></div>
        </div>
      </SearchBarContext.Provider>
    </FilePathContext.Provider>
  );
};

export default HomePage;